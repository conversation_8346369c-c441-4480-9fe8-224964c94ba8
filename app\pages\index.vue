<template>
  <div
       class="flex items-center justify-center min-h-screen p-4">
    <div class="flex flex-col items-center justify-between w-full align-center">
      <!-- 火箭图标 -->
      <div class="flex flex-col items-center justify-center mb-8">
        <div class="flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg">
          <img src="@/assets/images/logo.png" alt="" srcset="">
        </div>
        <!-- 标题 -->
        <div class="mb-8 text-center">
          <h1 class="mb-2 text-2xl font-bold text-gray-800">
            Estimate your academic ability
          </h1>
          <div class="text-sm font-light text-gray-600">
            Enter your X account to see which mutual followers have the same academic ability as you.
          </div>
        </div>

        <!-- 输入框 -->
        <div class="mb-8">
          <UInput
                  v-model="username"
                  @keydown.enter="handleSubmit"
                  @submit="handleSubmit"
                  placeholder="Enter X username"
                  size="lg"
                  class=""
                  :ui="{
                    base: 'relative block disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none w-[680px] h-[64px] flex justify-between items-center bg-white rounded-[80px] border-[4px] border-[#F2F2F259]  shadow-[0px_4px_24px_0px_#1226420D] px-6 py-3',
                    rounded: 'rounded-[80px]',
                    placeholder: 'placeholder-gray-400 dark:placeholder-gray-500',
                    size: {
                      lg: 'text-base'
                    },
                    color: {
                      white: {
                        outline: ''
                      }
                    }
                  }">
            <template #trailing>
              <UButton
                       @click="handleSubmit"
                       :disabled="!username.trim()"
                       variant="ghost"
                       class=""
                       :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                <img src="~/assets/images/ai.png" alt="Refresh Icon" class="w-8 h-8"></img>
              </UButton>
            </template>
          </UInput>
        </div>

        <!-- 底部导航点 -->
        <div class="flex justify-center mb-8 space-x-2">
          <UButtonGroup>
            <UButton variant="ghost" square aria-label="刷新" class="cursor-pointer" :ui="{
              base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent',
            }">
              <img src="~/assets/images/x.png" alt="Refresh Icon" class="w-8 h-8">
            </UButton>
            <UButton variant="ghost" square aria-label="刷新" class="cursor-pointer" :ui="{
              base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent',
            }">
              <img src="~/assets/images/tg.png" alt="Refresh Icon" class="w-8 h-8">
            </UButton>
            <UButton variant="ghost" square aria-label="刷新" class="cursor-pointer" :ui="{
              base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent',
            }">
              <img src="~/assets/images/github.png" alt="Refresh Icon" class="w-8 h-8">
            </UButton>
          </UButtonGroup>

        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
const username = ref('')

const handleSubmit = async () => {
  console.log('submit', username.value)
  let name = username.value.trim()
  const { data } = await useFetch(`/api/profileImage?username=${name}`);
  if (data.value.get_profile_image) {
    // 导航到结果页面
    navigateTo(`/result?username=${encodeURIComponent(name)}&image=${encodeURIComponent(data.value?.get_profile_image || '')}`)
  }
}
</script>
