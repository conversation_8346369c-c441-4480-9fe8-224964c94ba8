export default defineEventHandler(async (event) => {
  // You can access query parameters, headers, etc. from the `event` object
  const { name = 'World' } = getQuery(event);

  //https://api.flizaos.com/get_profile_image?username=flizaos

  console.log('get profile image for', name)

  // Here you can perform any server-side logic, like fetching data or processing input
  const users = await $fetch('https://api.flizaos.com/get_profile_image?username=' + name)
    .catch((error) => {
      console.error('Error fetching profile image:', error);
      return { profile_image_url: 'https://example.com/default-image.png' }; // Fallback image
    });
  return {
    get_profile_image: users.profile_image_url || 'https://example.com/default-image.png',
  };
});
