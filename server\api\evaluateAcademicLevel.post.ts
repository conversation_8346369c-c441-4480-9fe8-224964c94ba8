interface userProfile {
}

export default defineEventHandler(async (event) => {
  // 从请求体 (request body) 中读取数据
  // readBody 是 Nitro 的一个辅助函数
  const body = await readBody(event);

  // 简单的验证
  if (!body.username) {
    // 设置 HTTP 状态码并返回错误
    setResponseStatus(event, 400); // Bad Request
    return { error: 'Missing username' };
  }

  //https://api.flizaos.com/evaluate_academic_level
  const users: userProfile = await $fetch<userProfile>('https://api.flizaos.com/evaluate_academic_level', {
    method: 'POST',
    body: body,
    headers: {
      'Content-Type': 'application/json'
    }
  })
    .catch((error) => {
      console.error('Error fetching profile image:', error);
      return {
      };
    });

  // 返回成功信息
  setResponseStatus(event, 201); // Created
  return {};
});
