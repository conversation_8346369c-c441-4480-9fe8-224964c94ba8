export default defineEventHandler(async (event) => {
  // 从请求体 (request body) 中读取数据
  // readBody 是 Nitro 的一个辅助函数
  const body = await readBody(event);

  // 简单的验证
  if (!body.name) {
    // 设置 HTTP 状态码并返回错误
    setResponseStatus(event, 400); // Bad Request
    return { error: 'Missing name' };
  }

  // 在真实应用中，你会将数据插入到数据库
  console.log('Received new user:', body.name);

  // 返回成功信息
  setResponseStatus(event, 201); // Created
  return { message: 'User added successfully', user: body };
});
